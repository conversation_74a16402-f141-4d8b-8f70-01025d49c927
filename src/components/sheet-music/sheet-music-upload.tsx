import { Box, Button, ButtonGroup, Center, createDisclosure, HStack, Icon, Input, InputGroup, InputLeftAddon, Modal, ModalBody, ModalCloseButton, ModalContent, <PERSON>dal<PERSON><PERSON>er, ModalHeader, ModalOverlay, Textarea, VStack } from "@hope-ui/solid";
import { cloneDeep, isEmpty, isEqual } from "lodash-es";
import { FaSolidFileArrowUp } from "solid-icons/fa";
import { Component, createEffect, createMemo, createSignal, onCleanup, onMount } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import { z } from "zod";
import { SheetMusicRequest, SheetMusicCategory, SheetMusicDifficultyLevel, SheetMusicConst, SheetMusicGenreTags } from "~/models/sheet-music-dbo.models";
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { AppVPSequencerFileLoad, AppVPSequencerTrack } from "~/proto/pianorhythm-app-renditions";
import AppService from "~/services/app.service";
import DisplaysService, { Displays } from "~/services/displays.service";
import { SheetMusicService } from "~/services/sheet-music.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { ab2str } from "~/util/helpers";
import SwalPR from "~/util/sweetalert";
import VPSheetSequencerControlsUI from "../midi-player/midi-vp-sequencer-ui";
import { SheetMusicContentUpload } from "./sheet-music-common";
import MotionFadeIn from "../motion/motion.fade-in";
import abcjs from "abcjs";
import "abcjs/abcjs-audio.css";
import { COMMON } from "~/util/const.common";
import NotificationService from "~/services/notification.service";
import { CreateSelectList } from "./components/create-select-list";
import { AbcNotationDetector } from "./components/abc-notation-detector";

import { useSheetMusicForm } from "./hooks/use-sheet-music-form";

const SheetMusicUpload: Component = () => {
  const DISPLAY_KEY = "SHEET_MUSIC_UPLOAD_MODAL";
  const { isOpen, onOpen, onClose } = createDisclosure();
  const appService = useService(AppService);
  const displayService = useService(DisplaysService);
  const sheetMusicService = useService(SheetMusicService);
  const sfxService = useService(SoundEffectsService);
  const [lastModal, setLastModal] = createSignal<keyof Displays>();

  const isEditMode = createMemo(() => sheetMusicService().editMode());
  const activeData = createMemo(() => sheetMusicService().activeViewerData());

  const form = useSheetMusicForm({
    isEditMode: isEditMode(),
  });

  onMount(() => {
    const currentActiveData = activeData();

    if (isEditMode() && currentActiveData) {
      let recentModal = displayService().getLastModalsOpened().indexOf("SHEET_MUSIC_DETAILS_MODAL");

      // Return to repo if we were last in the repo
      if (recentModal > -1) setLastModal("SHEET_MUSIC_REPO_MODAL");

      const source = currentActiveData.data;
      if (source) {
        const initialData: SheetMusicRequest = {
          title: source.title || "",
          songArtist: source.songArtist || undefined,
          songAlbum: source.songAlbum || undefined,
          tags: source.tags || [],
          description: source.description || undefined,
          category: source.category || SheetMusicCategory.VirtualPiano,
          privacy: (source.privacyStatus || "Public") as "Public" | "Unlisted" | "Private",
          difficultyLevel: source.difficultyLevel || SheetMusicDifficultyLevel.Unknown,
          tempo: source.bpm || 120,
          data: ab2str(currentActiveData.file.data as any),
          totalTime: undefined,
        };

        form.setInitialFormData(initialData);
      }
    } else {
      let uploadData = sheetMusicService().newSheetFromViewerUploadData();
      if (uploadData?.data) {
        form.updateField("data", uploadData.data);
      }
    }
  });

  onCleanup(() => {
    SwalPR(sfxService).close();
    sheetMusicService().setNewSheetFromViewerUploadData({ data: undefined });
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.VPSequencerStop,
    }), true);

    if (lastModal()) displayService().setDisplay(lastModal()!, true);
  });

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
    sheetMusicService().setEditMode(false);
    sheetMusicService().setActiveViewerData(undefined);
  }

  createEffect(async () => {
    if (displayService().getDisplay(DISPLAY_KEY)) {
      onOpen();
    } else { onClose(); }
  });

  const isVpCategory = () => form.fields.category == SheetMusicCategory.VirtualPiano;
  const isAbcCategory = () => form.fields.category == SheetMusicCategory.ABCMusicNotation;

  const handleAbcMetaDetected = (meta: { title?: string; artist?: string; album?: string; bpm?: number }) => {
    const updates: Partial<SheetMusicRequest> = {};
    if (meta.title) updates.title = meta.title;
    if (meta.artist) updates.songArtist = meta.artist;
    if (meta.album) updates.songAlbum = meta.album;
    if (meta.bpm) updates.tempo = meta.bpm;

    form.updateFields(updates);
  };

  const handleCategoryChange = (category: SheetMusicCategory) => {
    form.updateField("category", category);
  };

  return (<>
    <Modal
      centered opened={isOpen()} onClose={closeModal}
      scrollBehavior={"inside"}
      closeOnOverlayClick={false}
      size="full"
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader><Icon as={FaSolidFileArrowUp} /> Sheet Music - {isEditMode() ? "Edit" : "New Upload"}</ModalHeader>

        <ModalBody padding={30} >
          {!isAbcCategory() &&
            <AbcNotationDetector
              data={form.fields.data}
              category={form.fields.category || SheetMusicCategory.VirtualPiano}
              onMetaDetected={handleAbcMetaDetected}
              onCategoryChange={handleCategoryChange}
            />
          }

          <Box>
            {(form.fields.data && isVpCategory()) &&
              <MotionFadeIn>
                <Box
                  borderTopRadius={5}
                  background={"$primaryDark1"} paddingTop={10}>
                  <VPSheetSequencerControlsUI
                    input={AppVPSequencerFileLoad.create({
                      data: cloneDeep(form.fields.data),
                      fileName: cloneDeep(form.fields.title) || "Untitled",
                      tracks: [
                        AppVPSequencerTrack.create({
                          index: 0,
                          tempo: form.fields.tempo || 120,
                        })
                      ]
                    })}
                  />
                </Box>
              </MotionFadeIn>
            }

            <SheetMusicContentUpload
              allowCreateNew
              categoryType={form.fields.category as SheetMusicCategory}
              isVPSheet={form.fields.category == SheetMusicCategory.VirtualPiano}
              displayDataAfterUpload
              value={form.fields.data}
              onNewBodyContentUpdate={(text) => { form.updateField("data", text); }}
              onLoad={(data) => { form.updateField("data", atob(data.data as any)); }}
            />
          </Box>

          <VStack spacing="$4" marginTop={50}>
            {/* Title */}
            <InputGroup>
              <InputLeftAddon>Title</InputLeftAddon>
              <Input
                value={form.fields.title}
                invalid={!!form.validateField("title")}
                onInput={(e) => form.updateField("title", (e.target as any).value || "")}
                placeholder="title" maxlength={SheetMusicConst.MaxSongName} />
            </InputGroup>

            {/* Artist & Album */}
            <HStack spacing="$7" w="100%">
              <InputGroup>
                <InputLeftAddon>Artist</InputLeftAddon>
                <Input
                  value={form.fields.songArtist}
                  invalid={!!form.validateField("songArtist")}
                  onInput={(e) => form.updateField("songArtist", (e.target as any).value || undefined)}
                  placeholder="original artist" maxlength={SheetMusicConst.MaxArtistNameLength} />
              </InputGroup>
              <InputGroup>
                <InputLeftAddon>Album</InputLeftAddon>
                <Input
                  value={form.fields.songAlbum}
                  invalid={!!form.validateField("songAlbum")}
                  onInput={(e) => form.updateField("songAlbum", (e.target as any).value || undefined)}
                  placeholder="original artist's album" maxlength={SheetMusicConst.MaxArtistAlbumLength} />
              </InputGroup>
            </HStack>

            {/* Description */}
            <InputGroup>
              <InputLeftAddon>Description</InputLeftAddon>
              <Textarea
                value={form.fields.description}
                invalid={!!form.validateField("description")}
                onInput={(e) => form.updateField("description", (e.target as any).value || undefined)}
                minH="5rem" maxH="15rem" resize={"vertical"}
                maxlength={SheetMusicConst.MaxDescriptionLength} />
            </InputGroup>

            {/* Tempo */}
            <InputGroup>
              <InputLeftAddon>Tempo</InputLeftAddon>
              <Input
                type="number"
                value={form.fields.tempo}
                invalid={!!form.validateField("tempo")}
                onInput={(e) => form.updateField("tempo", parseInt((e.target as any).value) || 120)}
                placeholder="tempo/bpm"
                min={1}
                max={1000}
              />
            </InputGroup>

            {/* Tags */}
            <InputGroup>
              <InputLeftAddon>Tags</InputLeftAddon>
              <CreateSelectList
                data={SheetMusicGenreTags.map(tag => ({ value: tag, label: tag }))}
                isMultiple
                defaultValue={form.fields.tags}
                invalid={!!form.validateField("tags")}
                onSetValue={(val) => form.updateField("tags", val as string[])}
                placeholder="Choose some tags..." />
            </InputGroup>

            {/* Category */}
            <InputGroup>
              <InputLeftAddon>Category</InputLeftAddon>
              <CreateSelectList
                defaultValue={String(form.fields.category)}
                valueSignalChange={() => form.fields.category}
                invalid={!!form.validateField("category")}
                onSetValue={(val) => form.updateField("category", val as SheetMusicCategory)}
                data={[
                  { value: SheetMusicCategory.MultiplayerPiano, label: SheetMusicCategory.MultiplayerPiano },
                  { value: SheetMusicCategory.VirtualPiano, label: SheetMusicCategory.VirtualPiano },
                  { value: SheetMusicCategory.ABCMusicNotation, label: "A text-based music notation system and the de facto standard for folk and traditional music." },
                ]}
                placeholder="Choose a category..." />
            </InputGroup>

            {/* Difficulty */}
            <InputGroup>
              <InputLeftAddon>Difficulty</InputLeftAddon>
              <CreateSelectList
                defaultValue={form.fields.difficultyLevel}
                invalid={!!form.validateField("difficultyLevel")}
                onSetValue={(val) => form.updateField("difficultyLevel", val as SheetMusicDifficultyLevel)}
                data={[
                  { value: SheetMusicDifficultyLevel.Beginner },
                  { value: SheetMusicDifficultyLevel.BeginnerPlus },
                  { value: SheetMusicDifficultyLevel.Intermediate },
                  { value: SheetMusicDifficultyLevel.IntermediatePlus },
                  { value: SheetMusicDifficultyLevel.Advanced },
                  { value: SheetMusicDifficultyLevel.AdvancedPlus },
                  { value: SheetMusicDifficultyLevel.Unknown },
                ]}
                placeholder="Choose an appropriate difficulty..." />
            </InputGroup>

            {/* Privacy */}
            <InputGroup>
              <InputLeftAddon>Privacy Level</InputLeftAddon>
              <CreateSelectList
                defaultValue={form.fields.privacy}
                invalid={!!form.validateField("privacy")}
                onSetValue={(val) => form.updateField("privacy", val as "Public" | "Unlisted" | "Private")}
                data={["Public", "Private", "Unlisted"].map(item => ({ value: item }))}
                placeholder="Choose a privacy level" />
            </InputGroup>
          </VStack>
        </ModalBody>

        <ModalFooter h="3rem" padding={0}>
          <ButtonGroup size={"sm"} spacing="$2" padding={"$2"}>
            <Button variant={"outline"} onClick={closeModal}>Cancel</Button>

            {/* Submit */}
            <Button
              onClick={async () => {
                let message = "Your changes will have to be reapproved after submitting.";
                if (appService().isClientMod()) message = "Your changes will be submitted once you click OK!";
                if (!isEditMode() && form.fields.privacy == "Public") message = "Your upload will have to be approved before it shows up on the public listing.";

                SwalPR(sfxService).fire({
                  icon: "info",
                  html: message,
                  showCancelButton: true,
                  confirmButtonText: "Okay!",
                  showCloseButton: true,
                  allowEscapeKey: true,
                  allowEnterKey: true,
                  showLoaderOnConfirm: true,
                  allowOutsideClick: () => !SwalPR().isLoading(),
                  preConfirm: async () => {
                    try {
                      const data = await form.getFormData();
                      const id = activeData()?.data.id;
                      return isEditMode()
                        ? sheetMusicService().updateSheetMusic(data, id)
                        : sheetMusicService().uploadSheetMusic(data);
                    } catch (error) {
                      throw new Error("Invalid input");
                    }
                  }
                }).then((result) => {
                  if (result.isConfirmed) {
                    NotificationService.show({
                      title: "Sheet Music Upload",
                      description: "Sheet music upload success!",
                      type: "success",
                    });
                    displayService().setDisplay("SHEET_MUSIC_UPLOAD_MODAL", false);
                  }
                }).catch((err) => {
                  console.error(err);
                  let errorMessage = `Error: <b>${err.message}</b>`;

                  try {
                    let validation_errors = JSON.parse(err.message) as string[][];
                    errorMessage = ``;
                    validation_errors.forEach((error) => {
                      let key = error[0];
                      let desc = error[1];
                      errorMessage += `<br>[<b>${key}</b>]: ${desc}`;
                    });
                  } catch { }

                  SwalPR(sfxService).fire({
                    icon: "error",
                    html: `
                          Oof, sorry. Failed to upload sheet music!
                          <br><br>
                          ${errorMessage}
                          <br><br>
                          Please try again.
                        `
                  });
                });
              }}
              disabled={!form.canSubmit}
              background={"$primaryDark1"}
            >
              Submit
            </Button>
          </ButtonGroup>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

export default SheetMusicUpload;