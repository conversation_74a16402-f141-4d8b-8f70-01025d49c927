import { SearchDriverOptions } from "@elastic/search-ui";
import { Anchor, Badge, Box, Button, Center, HStack, Image, VStack } from "@hope-ui/solid";
import createStorage from '@solid-primitives/local-store';
import { Title } from "@solidjs/meta";
import { createAsync, useAction, useNavigate, useParams } from "@solidjs/router";
import { FaSolidMusic } from "solid-icons/fa";
import { createReaction, createSignal, lazy, onCleanup, onMount, Show, Suspense } from "solid-js";
import { useService } from "solid-services";
import MotionFadeIn from "~/components/motion/motion.fade-in";
import { GenericDefaultBadges } from "~/components/search-ui/generic-repo";
import GenericDisplayCard, { GenericDisplayCardItem } from "~/components/search-ui/search-ui.generic-display";
import { MyAPIConnector } from "~/components/search-ui/search-ui.types";
import { getMemberSessionInfo, login } from "~/lib";
import { getSheetMusicQuery } from "~/lib/sheet_music";
import { SheetMusicDto, SheetMusicDtoHelpers } from "~/models/sheet-music-dbo.models";
import { rolesFromJSON, UserClientDto, UserDto } from "~/proto/user-renditions";
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import CoreWasmService from "~/services/core-wasm.service";
import DisplaysService from "~/services/displays.service";
import LoginService from "~/services/login.service";
import MidiPlayerService from "~/services/midi-player.service";
import NotificationService from "~/services/notification.service";
import ResourceService from "~/services/resource.service";
import { SheetMusicService } from "~/services/sheet-music.service";
import { TauriService } from "~/services/tauri.service";
import { AudioReverb, AudioReverbPreset, DEFAULT_SOUNDFONT } from "~/types/audio.types";
import { UserDtoUtil } from "~/types/user-helper";
import { IDS, IMAGES } from "~/util/const.common";
import { MIDI } from "~/util/const.midi";
import { createLoginFormData } from "~/util/helpers";

const SheetMusicDetails = lazy(() => import("~/components/sheet-music/sheet-music-details"));
const SidebarDocsPanel = lazy(() => import("~/components/modals/sidebar-docs-panel"));
const SearchUIView = lazy(() => import("~/components/search-ui/search-ui.base"));
const LoginNavbarProfile = lazy(() => import("~/components/login/login-navbar-profile"));
const SheetMusicUploadModal = lazy(() => import("~/components/sheet-music/sheet-music-upload"));

const config: SearchDriverOptions = {
  debug: false, //COMMON.IS_DEV_MODE,
  trackUrlState: false,
  alwaysSearchOnInitialLoad: true,
  apiConnector: new MyAPIConnector("/api/v1/sheet_music/searchDriver"),
  searchQuery: {
    search_fields: {
      "title": {},
      "songAlbum": {},
      "uuid": {},
      "creatorUsername": {},
    },
    facets: {
      "category": { type: "value" },
      "difficulty": { type: "value" },
      "tags": { type: "value" },
    }
  }
};

const RESULTS_PER_PAGE = [9, 18, 36];

const MainPageNavigationBar = () => {
  const appService = useService(AppService);
  const displaysService = useService(DisplaysService);
  const resourceService = useService(ResourceService);
  const navigate = useNavigate();
  const logo = createAsync(() => resourceService().getAssetImage(IMAGES.LOGO));

  return (<>
    <HStack
      p="$2" w="100%"
      justifyContent="flex-end" position={"relative"}
      borderBottom={"1px solid $neutral11"} mb={7}
      fontSize={"0.85em"}
      textTransform={"lowercase"}
    >
      <HStack spacing="$2" justifySelf={"flex-end"} position={"absolute"} left={20}>
        {logo() && <Image
          loading="lazy"
          w="50px"
          alt="PianoRhythm Logo"
          borderRadius={"50%"}
          src={logo()}
        />
        }
        <Anchor
          color="$neutral11"
          href="."
          onClick={(e) => { e.preventDefault(); navigate("/", { resolve: true }); }}
          className="nav-link"
        >
          Home
        </Anchor>
        <Anchor
          color="$neutral11"
          href="/login"
          className="nav-link"
        >
          Login
        </Anchor>
        <Anchor
          color="$neutral11"
          className="nav-link"
          href="/register"
        >
          Register
        </Anchor>
        {appService().isClientMember() &&
          <Button
            __tooltip_title="Upload your own sheet music"
            onClick={() => displaysService().setDisplay("SHEET_MUSIC_UPLOAD_MODAL", true)}
            size="sm">Upload</Button>
        }
      </HStack>
      <Suspense>
        <LoginNavbarProfile />
      </Suspense>
    </HStack>
  </>);
};

export default function MainPage() {
  const params = useParams();
  const user = createAsync(() => getMemberSessionInfo(), {
    deferStream: true,
    initialValue: { username: "", usertag: "", roles: [] }
  });
  const sheetMusicService = useService(SheetMusicService);
  const appService = useService(AppService);
  const loginService = useService(LoginService);
  const audioService = useService(AudioService);
  const displayService = useService(DisplaysService);
  const midiPlayerService = useService(MidiPlayerService);

  const [selectedSheets, setSelectedSheets] = createSignal<SheetMusicDto[]>([]);
  const [driverOptions, setDriverOptions] = createSignal({ ...config });
  const [defaultResultsPerPage, setDefaultResultsPerPage] = createSignal(RESULTS_PER_PAGE[0]);
  const [activeDetailID, setActiveDetailID] = createSignal<string>();
  const [localStorageSettings, setLocalStorageSettings] = createStorage<{
    sheetMusicRepoResultsPerPage: string;
  }>();

  onMount(async () => {
    midiPlayerService().initialize();

    try {
      appService().loadCoreWasm(new CoreWasmService(), new TauriService())
        .then(audioService().initialize)
        .then(() => audioService().loadSoundfont(DEFAULT_SOUNDFONT, false, true))
        .then(() => appService().coreService()?.add_socket(MIDI.MIDI_SYNTH_SOCKET_ID))
        .then(() => {
          // Set the reverb
          let preset = AudioReverb.FromPreset(AudioReverbPreset.Church);
          audioService().setReverbLevel(preset.AUDIO_REVERB_LEVEL);
          audioService().setReverbRoomsize(preset.AUDIO_REVERB_ROOMSIZE);
          audioService().setReverbDamp(preset.AUDIO_REVERB_DAMP);
          audioService().setReverbWidth(preset.AUDIO_REVERB_WIDTH);

          NotificationService.hide(IDS.AUDIO_INITIALIZATION);
          NotificationService.hide(IDS.LOADING_SOUNDFONT);
        });
    } catch (e) {
      console.error(e);
    }

    setDriverOptions(o => {
      let facets = o.searchQuery?.facets;
      if (facets) { facets.favorites = { type: "value" }; }
      if (o.searchQuery && o.searchQuery.facets) o.searchQuery.facets = facets;
      return { ...o };
    });

    if (appService().doesClientHaveSheetMusicFullAccess()) {
      setDriverOptions(o => {
        let facets = o.searchQuery?.facets;
        if (facets) { facets.approved = { type: "bool" }; }
        if (o.searchQuery && o.searchQuery.facets) o.searchQuery.facets = facets;
        return { ...o };
      });
    }

    sheetMusicService().setRepoActive(true);

    let resultsPerPage = localStorageSettings.sheetMusicRepoResultsPerPage;
    if (resultsPerPage) { resultsPerPage = JSON.parse(resultsPerPage) as string; }
    try {
      if (resultsPerPage != null) setDefaultResultsPerPage(parseInt(resultsPerPage));
    } catch { }

    // Check if sheet music exists
    if (params.id) {
      let sheet = await getSheetMusicQuery(decodeURI(params.id!));
      if (sheet) {
        setActiveDetailID(sheet.id);
        sheetMusicService().setActiveDetailID(sheet.id);
      }
    }
  });

  onCleanup(() => {
    sheetMusicService().setRepoActive(false);
    sheetMusicService().setSearchDriver(undefined);

    // Reset services
    audioService().onDisconnect();
    midiPlayerService().onDisconnect();
    appService().onDisconnect();
    sheetMusicService().onDisconnect();
  });

  const loginAction = useAction(login);
  const onGetMember = createReaction(async () => {
    let targetUser = user();

    if (!user()) {
      let sessionInfo = await loginService().onLogin(loginAction(createLoginFormData("", undefined, true)));
      if (!sessionInfo) return;
      targetUser = sessionInfo;
    }

    if (!targetUser) return;

    loginService().setCurrentLoggedInUsername({
      usertag: targetUser.usertag,
      username: targetUser.username,
    });
    loginService().setLoggedIn(true);
    loginService().onUserAutoLoginNotification(targetUser.username);
    appService().clearClient();
    appService().onClientLoaded(UserClientDto.create({
      userDto: UserDto.create({
        usertag: targetUser.usertag,
        username: targetUser.username,
        roles: targetUser.roles.map(rolesFromJSON),
      }),
    }));
  });
  onGetMember(user);

  const onCardMount = (element: HTMLDivElement, item: SheetMusicDto) => {
    let canApproveSheetMusic = appService().doesClientHaveSheetMusicFullAccess();
    let canDelete = canApproveSheetMusic || appService().client().usertag == item.creatorUsername;

    // let subscription = useContextMenu(element, [
    //   { value: `Id: ${item.id}`, enabled: false },
    //   { value: `Title: ${item.title}`, enabled: false },
    //   {
    //     value: `Open in Viewer`, enabled: item.category != SheetMusicCategory.MusicXML, type: "button", onclick: async () => {
    //       await sheetMusicService().loadSheetMusic(item);
    //       onClose();
    //     }
    //   },
    //   (canApproveSheetMusic && !item.approved) && {
    //     value: `Approve`, type: "button", onclick: async () => {
    //       sheetMusicService().startApprovalProcess(item, "approve");
    //     }
    //   },
    //   (canApproveSheetMusic && item.approved === true) && {
    //     value: `Disapprove`, type: "button", onclick: async () => {
    //       sheetMusicService().startApprovalProcess(item, "disapprove");
    //     }
    //   },
    //   (canDelete) && {
    //     value: `Delete`, type: "button", onclick: async () => {
    //       sheetMusicService().deleteSheetMusicDialogue(item);
    //     }
    //   },
    // ].filter(Boolean) as Item[]);
    // onCleanup(() => subscription.unsubscribe());
  };

  // const getApprovableSheets = () => selectedSheets().filter(x => !x.approved);
  // const getDisapprovableSheets = () => selectedSheets().filter(x => x.approved);
  // const canApproveMultiple = () => selectedSheets().some(x => !x.approved);
  // const canDisapproveMultiple = () => selectedSheets().some(x => x.approved);
  // const getApprovableCount = () => getApprovableSheets().length;
  // const getDisapprovableCount = () => getDisapprovableSheets().length;

  return <Box
    h="100%" w="100%"
    overflow={"scroll"}
    background={"$primaryDark1"}
  >
    <Title>PianoRhythm - Sheet Music</Title>
    <MainPageNavigationBar />

    {displayService().getDisplay("SIDEBAR_HELP_DOCS") &&
      <Suspense>
        <SidebarDocsPanel />
      </Suspense>
    }

    <Show when={activeDetailID()}>
      <Suspense>
        <SheetMusicDetails
          id={activeDetailID() as string}
          onClose={() => setActiveDetailID(undefined)}
          showHeader={false}
          hideOpenInViewerIcon={true}
          scrollBehavior="inside"
        />
      </Suspense>
    </Show>

    <Center w="90%" justifyContent={"center"} m="auto">
      <VStack w="100%">
        <HStack w="100%" p={"$2"} spacing="$2" background={"$primaryDarkAlpha"}>
          <FaSolidMusic size={14} />
          <Box>Sheet Music Repo</Box>
        </HStack>
        <Suspense>
          <SearchUIView<SheetMusicDto>
            config={driverOptions()}
            initialSearchTerm={params.id ? decodeURI(params.id!) : undefined}
            defaultResultsPerPage={defaultResultsPerPage()}
            amountList={RESULTS_PER_PAGE}
            multiSelectEnabled={appService().doesClientHaveSheetMusicFullAccess()}
            onMultiSelect={setSelectedSheets}
            onMount={(driver) => {
              sheetMusicService().setSearchDriver(driver);
            }}
            onRenderItem={(idx: number) => (item: SheetMusicDto) => {
              let props: GenericDisplayCardItem = {
                title: item.title,
                category: item.category,
                subtitle: item.creatorUsername,
                thumbnailImage: IMAGES.DEFAULT_SHEETMUSIC_BG_IMAGE,
                avatarImage: item.creatorUsername,
                badges: [
                  <Badge
                    __tooltip_title={`Difficulty Level: ${item.difficultyLevel}`}
                    fontSize={"8px !important"}
                    background={SheetMusicDtoHelpers.DifficultyLevelToColor(item.difficultyLevel)}
                  >{item.difficultyLevel}
                  </Badge>,
                  ...GenericDefaultBadges(item as any),
                ],
                onMount: (element) => onCardMount(element, item),
                onClick: () => {
                  setActiveDetailID(item.id);
                  sheetMusicService().setActiveDetailID(item.id);
                }
              };
              return <MotionFadeIn delay={0.1 * idx} duration={0.2}><GenericDisplayCard item={props} /></MotionFadeIn>;
            }}
            onDriverStateChange={(state) => {
              if (state.resultsPerPage && RESULTS_PER_PAGE.includes(state.resultsPerPage))
                setLocalStorageSettings("sheetMusicRepoResultsPerPage", JSON.stringify(state.resultsPerPage));
            }}
            onFacetLabelKeyMap={(key) => {
              return key.toLowerCase();
            }}
          />
        </Suspense>
      </VStack>
    </Center>

    {displayService().getDisplay("SHEET_MUSIC_UPLOAD_MODAL") &&
      <Suspense><SheetMusicUploadModal /></Suspense>
    }
  </Box>;
}